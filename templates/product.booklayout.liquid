{% assign guestCheckout = false %}

{% for c in product.collections %}
  {% if c.metafields.custom.guest_checkout == true %}
    {% assign guestCheckout = true %}
    {% break %}
  {% endif %}
{% endfor %}

{%- liquid
assign is_member_customer = false
if customer and customer.metafields.custom.ismember == true or customer.metafields.custom.IsMember == true 
  assign is_member_customer = true
endif

assign tier_option_exist = false
assign customer_variant_id = nil

for option in product.options_with_values
    assign option_name = option.name | downcase
  if option_name == 'ratecode'

    
    assign has_member = false
    assign has_list = false

    for value in option.values
      assign value_downcase = value | downcase
      if value_downcase == 'member'
        assign has_member = true
      endif
      if value_downcase == 'list'
        assign has_list = true
      endif
    endfor

    if has_member and has_list
      assign tier_option_exist = true
    endif

    for value in option.values
      assign value_downcase = value | downcase
      assign matching_variant = product.variants | where: 'option1', value | first

      if matching_variant
        if value_downcase == 'member' and is_member_customer
          assign customer_variant_id = matching_variant.id
        endif
        if value_downcase == 'list' and is_member_customer == false
          assign customer_variant_id = matching_variant.id
        endif
      endif
    endfor
  endif
endfor
-%}

<div class="product-container custom-product-section">
  <!-- Product Images -->
  <div class="product-images">
    {% if product.images.size > 0 %}
      <img src="{{ product.featured_image | img_url: 'master' }}" alt="{{ product.title }}" id="mainImage" class="main-image">
      <div class="thumbnails">
        {% for image in product.images %}
          <img src="{{ image | img_url: 'compact' }}" onclick="swapImage(this)">
        {% endfor %}
      </div>
    {% endif %}
  </div>

  <!-- Product Info -->
  <div class="product-info custom-product-section">
    <h1 style="font-size: 36px;font-weight: 600;font-family: var(--font-body-family);">{{ product.title }}</h1>
    {% if product.metafields.product.code %}
    <p><strong>Product Code:</strong> {{ product.metafields.product.code }}</p>
    {% endif %}
    <p><strong>Order #:</strong> {{ product.handle }} | ISBN: {{ product.metafields.custom.isbn }}</p>
    {% if product.metafields.custom.author.value %}
    <p><strong>Authors: <AUTHORS>
    {% endif %}

    {% if product.metafields.product.shortdesc %}
    <p>{{ product.metafields.product.shortdesc | metafield_tag }}</p>
    {% else %}
    <p>{{ product.description }}</p>
    {% endif %}


    <!-- Prices -->
    {% render 'custom-price', product: product %}


    <!-- Quantity & Add to Cart -->
    <div class="add-to-cart">
      <form id="add-to-cart-form" method="post" action="/cart/add" data-customer-logged-in="{% if customer or guestCheckout %}true{% else %}false{% endif %}">
        {% if tier_option_exist and customer_variant_id %}
          <input type="hidden" name="id" value="{{ customer_variant_id }}">
        {% else %}
          <input type="hidden" name="id" value="{{ product.variants.first.id }}">
        {% endif %}
        <div class="product-form__input product-form__quantity">
          <label class="quantity__label form__label" for="Quantity-123">Quantity</label>
          <quantity-input class="quantity">
            <button class="quantity__button" type="button" name="minus">
              <span class="svg-wrapper">
                {{ 'icon-minus.svg' | inline_asset_content }}
              </span>
            </button>
            <input
              type="number"
              class="quantity__input"
              id="Quantity-123"
              name="quantity"
              value="1"
              min="1"
              step="1"
            >
            <button class="quantity__button" type="button" name="plus">
              <span class="svg-wrapper">
                {{ 'icon-plus.svg' | inline_asset_content }}
              </span>
            </button>
          </quantity-input>
        </div>
        <br>
        <button class='st-wishlist-button' data-type='details' data-handle='{{ product.handle }}'></button>
        <br>
        <button type="submit" class="product-form__submit button button--full-width button--secondary">
          Add to cart
        </button>
      </form>

      <!-- Social Share Icons -->
      {% render 'social-share-icons' %}
      
    </div>
  </div>
</div>

<!-- Tabs Section -->
<div class="tabs custom-product-section">
  <div class="tab-buttons">
    <button class="tab-btn active" data-tab="desc">DESCRIPTION</button>
    <button class="tab-btn" data-tab="info">Return Policy & Shipping Charges</button>
  </div>

  <!-- Description Tab -->
  <div id="desc" class="tab-content active">
    {{ product.description }}
  </div>

  <!-- Return Policy Tab -->
  <div id="info" class="tab-content">
    <p><strong>ASCP now ships internationally at low rates!</strong></p>
    <p>ASCP is currently unable to ship to the following countries: Afghanistan, Belarus, Bhutan, ... Yemen.</p>
    <p><strong>ASCP Book Return Policy:</strong><br>ASCP has a 30-day return policy for all book purchases...</p>
  </div>
</div>

<!-- related products -->
{% section 'related-products-custom' %}
<!-- related products end -->

<style>
  .custom-product-section {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

  .tab-buttons {
    display: flex;
  }

  .tab-buttons button {
    flex: 1;
    border: 0px solid #ccc;
    padding: 1rem 0;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    cursor: pointer;
    transition: background 0.3s;
  }

  .tab-buttons button.active {
    background: #fff;
    color: #c00;
    border-top: 3px solid #c00;
  }

  .tab-content {
    display: none;
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
  }

  .tab-content.active {
    display: block;
  }
  .related-products {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
  }

  .related-products h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: bold;
  }

  .related-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
  }

  .related-item {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    background-color: #fafafa;
    transition: box-shadow 0.3s;
  }

  .related-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .related-item img {
    max-width: 100%;
    margin-bottom: 1rem;
  }

  .related-item h3 {
    font-size: 1.1rem;
    margin: 0.5rem 0;
  }

  .related-item p {
    font-weight: bold;
    margin: 0.5rem 0;
  }

  .related-item button {
    padding: 0.5rem 1rem;
    background-color: #c00;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .related-item button:hover {
    background-color: #900;
  }

  body {
    font-family: Arial, sans-serif;
    max-width: 1100px;
    margin: auto;
    padding: 2rem;
    background-color: #fff;
    color: #333;
  }

  .product-container {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .product-images {
    flex: 1 1 40%;
  }

  .main-image {
    width: 100%;
    max-width: 400px;
    border: 1px solid #ddd;
    border-radius: 6px;
  }

  .thumbnails {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .thumbnails img {
    width: 60px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .product-info {
    flex: 1 1 55%;
  }

  .product-info h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .prices {
    margin: 1rem 0;
  }

  .prices span {
    display: block;
    font-weight: bold;
  }

  .add-to-cart {
    margin: 1.5rem 0;
  }

  .tabs {
    margin-top: 2rem;
  }

  .tab-content {
    display: none;
  }

  .tab-content.active {
    display: block;
    border-top: 1px solid #ccc;
    padding-top: 1rem;
  }


</style>
<script>
  function swapImage(el) {
    document.getElementById('mainImage').src = el.src;
  }
  const buttons = document.querySelectorAll('.tab-btn');
  const tabs = document.querySelectorAll('.tab-content');
  buttons.forEach(btn => {
    btn.addEventListener('click', () => {
      
      buttons.forEach(b => b.classList.remove('active'));
      tabs.forEach(t => t.classList.remove('active'));
      btn.classList.add('active');
      document.getElementById(btn.dataset.tab).classList.add('active');
    });
  });

  document.addEventListener('DOMContentLoaded', function() {
    var form = document.getElementById('add-to-cart-form');
    if (!form) return;
    form.addEventListener('submit', function(e) {
      
      var isLoggedIn = form.getAttribute('data-customer-logged-in') === 'true';
      if (!isLoggedIn) {
        e.preventDefault();
        let u = new URL(window.location.href);
        window.location.href = '/customer_authentication/login?return_to=' + u.pathname;
      }
      // else, allow normal submission
    });
  });
</script>