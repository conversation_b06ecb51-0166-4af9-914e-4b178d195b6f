{% layout settings.customer_layout %}

<div id="admin_header">
  <div class="action_link action_return note" id='return_to_store'><a href="/account">Return to Account page</a></div>

  <h2 class="title">Order {{ order.name }}</h2>
</div>

{% if order.cancelled %}
<div id="order_cancelled" class="flash notice">
  <h5 id="order_cancelled_title">Order Cancelled <span class="note">on {{ order.cancelled_at | date: "%B %d, %Y %I:%M%p" }}</span></h5>
  <span class="note">{{ order.cancel_reason }}</span>
</div>
{% endif %}

<div class="note order_date">Placed on {{ order.created_at | date: "%B %d, %Y %I:%M%p" }}</div>

<div id="order_address" class="group">
  <div id="order_payment">
    <h5 class="order_section_title">Billing Address</h5>
    <p><span class="note">Payment Status:</span> <span class="status_{{ order.financial_status }}">{{ order.financial_status }}</span></p>
    <div class="address note">
      <p>{{ order.billing_address.name }}</p>
      <p>{{ order.billing_address.company }}</p>
      <p>{{ order.billing_address.street }}</p>
      <p>{{ order.billing_address.city }}, {{ order.billing_address.province }}</p>
      <p>{{ order.billing_address.country }} {{ order.billing_address.zip }}</p>
      <p>{{ order.billing_address.phone }}</p>
    </div>
  </div>
  {% if order.shipping_address %}
    <div id="order_shipping">
      <h5 class="order_section_title">Shipping Address</h5>
      <p><span class="note">Fulfillment Status:</span> <span class="status_{{ order.fulfillment_status }}">{{ order.fulfillment_status }}</span></p>
      <div class="address note">
        <p>{{ order.shipping_address.name }}</p>
        <p>{{ order.shipping_address.company }}</p>
        <p>{{ order.shipping_address.street }}</p>
        <p>{{ order.shipping_address.city }}, {{ order.shipping_address.province }}</p>
        <p>{{ order.shipping_address.country }} {{ order.shipping_address.zip }}</p>
        <p>{{ order.shipping_address.phone }}</p>
      </div>
    </div>
  {% endif %}
</div>

<table id="order_details">
  <thead>
    <tr>
      <th>Product</th>
      <th>SKU</th>
      <th>Price</th>
      <th class="center">Quantity</th>
      <th class="total">Total</th>
    </tr>
  </thead>
  <tbody>
    {% for line_item in order.line_items %}
    <tr id="{{ line_item.id }}" class="{% cycle 'odd', 'even' %}">
      <td class="product">
        {{ line_item.title | link_to: line_item.product.url }} 
        {% if line_item.fulfillment %}
          <div class="note">
            Fulfilled {{ line_item.fulfillment.created_at | date: "%b %d" }}
            {% if line_item.fulfillment.tracking_number %}
              <a href="{{ line_item.fulfillment.tracking_url }}">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number}}</a>
            {% endif %}
          </div>
        {% endif %}
      </td>
      <td class="sku note">{{ line_item.sku }}</td>
      <td class="money">{{ line_item.price | money }}</td>
      <td class="quantity cente">{{ line_item.quantity }}</td>
      <td class="total money">{{ line_item.quantity | times: line_item.price | money }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr class="order_summary note">
      <td class="label" colspan="4">Subtotal:</td>
      <td class="total money">{{ order.subtotal_price | money }}</td>
    </tr>

    {% for discount in order.discounts %}
      <tr class="order_summary discount">
        <td class="label" colspan="4">{{ discount.code }} Discount:</td>
        <td class="total money">{{ discount.savings | money }}</td>
      </tr>
    {% endfor %}

    {% for shipping_method in order.shipping_methods %}
    <tr class="order_summary note">
      <td class="label" colspan="4">Shipping ({{ shipping_method.title }}):</td>
      <td class="total money">{{ shipping_method.price | money }}</td>
    </tr>
    {% endfor %}

    {% for tax_line in order.tax_lines %}
      <tr class="order_summary note">
        <td class="label" colspan="4">Tax ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%):</td>
        <td class="total money">{{ tax_line.price | money }}</td>
      </tr>
    {% endfor %}

    <tr class="order_summary order_total">
      <td class="label" colspan="4">Total:</td>
      <td class="total money">{{ order.total_price | money }}</td>
    </tr>
  </tfoot>
</table>
