{% layout settings.customer_layout %}

<div id="template">
  <div id="customer">
	  <!-- Create Customer -->
	  <div id="create-customer">
	    <div class="template_header">
	      <h2 class="title">Create Account</h2>
	    </div>

	    {% form 'create_customer' %}
	      {{ form.errors | default_errors }}

	      <div class="clearfix large_form">
	        <label for="first_name" class="login">First Name</label>
	        <input type="text" value="" name="customer[first_name]" id="first_name" class="large" size="30" />
	      </div>

	      <div class="clearfix large_form">
	        <label for="last_name" class="login">Last Name</label>
	        <input type="text" value="" name="customer[last_name]" id="last_name" class="large" size="30" />
	      </div>

	      <div class="clearfix large_form">
	        <label for="email" class="login">Email Address</label>
	        <input type="email" value="" name="customer[email]" id="email" class="large" size="30" />
	      </div>

	      <div class="clearfix large_form">
	        <label for="password" class="login">Password</label>
	        <input type="password" value="" name="customer[password]" id="password" class="large password" size="30" />
	      </div>

	      <div class="action_bottom">
	        <input class="btn" type="submit" value="Create" />
	        <span class="note">or <a href="{{ shop.url }}">Return to Store</a></span>
	      </div>
	    {% endform %}
	  </div><!-- /#create-customer -->
  </div>
</div>
