{% assign current_type = product.type %}
{% assign current_sub_topics = product.metafields.product.subtopics.value %}
{% assign current_cat = product.metafields.product.topics.value %}
{% assign high_match = '' %}
{% assign medium_match = '' %}
{% assign low_match = '' %}

{% for p in collections.all.products %}
  {% if p.id != product.id and p.available %}
    {% assign score = 0 %}

    {% if p.type == current_type %}
      {% assign score = score | plus: 2 %}
    {% endif %}

    {% if p.metafields.product.subtopics.value %}
      {% for sub in current_sub_topics %}
        subtopics
        {% if p.metafields.product.subtopics.value contains sub %}
          {% assign score = score | plus: 1 %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

    {% if p.metafields.product.topics.value and current_cat %}
      {% for currentCat in current_cat %}
        {% for loopPCat in p.metafields.product.topics.value %}
          {% if currentCat.desc and currentCat.desc == loopPCat.desc %}
            {% assign score = score | plus: 1 %}
          {% endif %}
        {% endfor %}
      {% endfor %}
    {% endif %}

    {% if score >= 3 %}
      {% assign high_match = high_match | append: p.handle | append: ',' %}
    {% elsif score == 2 %}
      {% assign medium_match = medium_match | append: p.handle | append: ',' %}
    {% elsif score == 1 %}
      {% assign low_match = low_match | append: p.handle | append: ',' %}
    {% endif %}
  {% endif %}
{% endfor %}
{% assign sorted_handles = high_match | append: medium_match | append: low_match %}
{% assign related_products = sorted_handles | split: ',' %}

{% if related_products.size > 0 %}
<div class="related-products custom-product-section">
  <h2>Related Books</h2>
  <div class="slider-container">
    <div class="slider-wrapper">
      {% for related_product in related_products %}
        {% assign rp = all_products[related_product] %}
        <div class="product-card related-item">
          <img src="{{ rp.featured_image | img_url: 'medium' }}" alt="{{ rp.title }}" class="product-image">
          <div class="product-info">
            <h3 class="product-title">{{ rp.title }}</h3>
            {% render 'custom-price', product: rp %}
            <a href="{{ rp.url }}"><button>View Details</button></a>
          </div>
        </div>
      {% endfor %}
    </div>
  {% if related_products.size > 4 %}
    <div class="slider-nav">
      <div class="slider-arrow prev">←</div>
      <div class="slider-arrow next">→</div>
    </div>
  </div>
  {% endif %}
</div>

<style>
  .slider-container {
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
  }

  .slider-wrapper {
      display: flex;
      transition: transform 0.5s ease-in-out;
  }

  .product-card {
      flex: 0 0 calc(25% - 20px);
      margin: 0 10px;
      background-color: #ffffff;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      min-width: calc(25% - 20px);
  }

  .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
  }

  .product-info {
      padding: 1rem;
  }

  .product-title {
      font-size: 1.2rem;
      margin-bottom: 0.5rem;
  }

  .slider-nav {
      display: flex;
      justify-content: center;
      margin-top: 1rem;
      gap: 1rem;
      padding-bottom: 10px;
  }

  .slider-arrow {
      background-color: #ffffff;
      border: 2px solid #ced4da;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
  }

  .slider-arrow:hover {
      border-color: #3a86ff;
      color: #3a86ff;
  }

  .slider-arrow.disabled {
      opacity: 0.5;
      cursor: not-allowed;
  }

  @media (max-width: 1024px) {
      .product-card {
          flex: 0 0 calc(33.333% - 20px) !important;
          min-width: calc(33.333% - 20px) !important;
      }
  }

  @media (max-width: 768px) {
      .product-card {
          flex: 0 0 calc(50% - 20px) !important;
          min-width: calc(50% - 20px) !important;
      }
  }

  @media (max-width: 480px) {
      .product-card {
          flex: 0 0 calc(100% - 20px) !important;
          min-width: calc(100% - 20px) !important;
      }
  }
  .custom-product-section{
    padding-bottom: 10px;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      const sliderWrapper = document.querySelector('.slider-wrapper');
      const prevBtn = document.querySelector('.prev');
      const nextBtn = document.querySelector('.next');
      const productCards = document.querySelectorAll('.product-card');
      
      let currentSlide = 0;
      let productsPerView = 4;
      
      // Function to calculate products per view based on screen size
      function calculateProductsPerView() {
          const screenWidth = window.innerWidth;
          if (screenWidth <= 480) {
              return 1;
          } else if (screenWidth <= 768) {
              return 2;
          } else if (screenWidth <= 1024) {
              return 3;
          } else {
              return 4;
          }
      }
      
      // Initialize slider
      function initSlider() {
          productsPerView = calculateProductsPerView();
          const totalSlides = Math.ceil(productCards.length / productsPerView);
          
          // Update slider position
          updateSlider();
          
          // Update arrow buttons
          prevBtn.classList.toggle('disabled', currentSlide === 0);
          nextBtn.classList.toggle('disabled', currentSlide >= totalSlides - 1);
      }
      
      // Update slider position
      function updateSlider() {
          const productWidth = productCards[0].offsetWidth + 20; // Include margin
          const offset = -currentSlide * productsPerView * productWidth;
          sliderWrapper.style.transform = `translateX(${offset}px)`;
          
          // Update arrow buttons
          const totalSlides = Math.ceil(productCards.length / productsPerView);
          prevBtn.classList.toggle('disabled', currentSlide === 0);
          nextBtn.classList.toggle('disabled', currentSlide >= totalSlides - 1);
      }
      
      // Event listeners for navigation
      prevBtn.addEventListener('click', () => {
          if (currentSlide > 0) {
              currentSlide--;
              updateSlider();
          }
      });
      
      nextBtn.addEventListener('click', () => {
          const totalSlides = Math.ceil(productCards.length / productsPerView);
          if (currentSlide < totalSlides - 1) {
              currentSlide++;
              updateSlider();
          }
      });
      
      // Handle window resize
      window.addEventListener('resize', function() {
          const newProductsPerView = calculateProductsPerView();
          if (newProductsPerView !== productsPerView) {
              productsPerView = newProductsPerView;
              currentSlide = 0; // Reset to first slide on resize
              updateSlider();
          }
      });
      
      // Initialize the slider
      initSlider();
  });
</script>
{% endif %}