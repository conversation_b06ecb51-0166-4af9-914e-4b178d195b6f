{% comment %}
  Renders product buy-buttons.
  Accepts:
  - product: {Object} product object.
  - block: {Object} passing the block information.
  - product_form_id: {String} product form id.
  - section_id: {String} id of section to which this snippet belongs.
  - show_pickup_availability: {<PERSON><PERSON><PERSON>} for the pickup availability. If true the pickup availability is rendered, false - not rendered (optional).

  Usage:
  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true %}
{% endcomment %}
<!-- check guest checkout -->
{% assign guestCheckout = false %}
{% for c in product.collections %}
  {% if c.metafields.custom.guest_checkout == true %}
    {% assign guestCheckout = true %}
    {% break %}
  {% endif %}
{% endfor %}

<!-- end check guest checkout  -->

{%- liquid
assign is_member_customer = false
if customer and customer and customer.metafields.custom.ismember == true or customer.metafields.custom.IsMember == true
  assign is_member_customer = true
endif

assign tier_option_exist = false
assign customer_variant_id = nil

for option in product.options_with_values
    assign option_name = option.name | downcase
  if option_name == 'ratecode'

    
    assign has_member = false
    assign has_list = false

    for value in option.values
      assign value_downcase = value | downcase
      if value_downcase == 'member'
        assign has_member = true
      endif
      if value_downcase == 'list'
        assign has_list = true
      endif
    endfor

    if has_member and has_list
      assign tier_option_exist = true
    endif

    for value in option.values
      assign value_downcase = value | downcase
      assign matching_variant = product.variants | where: 'option1', value | first

      if matching_variant
        if value_downcase == 'member' and is_member_customer
          assign customer_variant_id = matching_variant.id
        endif
        if value_downcase == 'list' and is_member_customer == false
          assign customer_variant_id = matching_variant.id
        endif
      endif
    endfor
  endif
endfor
-%}

<!-- if customer logged in or allow guest checkout then set true -->
{% assign is_customer_logged_in = false %}
{% if customer or guestCheckout %}
  {% assign is_customer_logged_in = true %}
{% endif %}

<div {{ block.shopify_attributes }}>
  {%- if product != blank -%}
    {%- liquid
      assign gift_card_recipient_feature_active = false
      if block.settings.show_gift_card_recipient and product.gift_card?
        assign gift_card_recipient_feature_active = true
      endif

      assign show_dynamic_checkout = false
        if block.settings.show_dynamic_checkout and gift_card_recipient_feature_active == false
          assign show_dynamic_checkout = true
      endif 
    -%}

    <button class='st-wishlist-button' data-type='details' data-handle='{{ product.handle }}'></button>

    <product-form
      class="product-form"
      data-hide-errors="{{ gift_card_recipient_feature_active }}"
      data-section-id="{{ section.id }}"
    >
      <div class="product-form__error-message-wrapper" role="alert" hidden>
        <span class="svg-wrapper">
          {{- 'icon-error.svg' | inline_asset_content -}}
        </span>
        <span class="product-form__error-message"></span>
      </div>

      {%- form 'product',
         product,
        id: 'add-to-cart-form',
        method: "post",
        action: '/cart/add',
        data-customer-logged-in: is_customer_logged_in
      -%}
        {% if tier_option_exist and customer_variant_id %}
          <input type="hidden" class="normal" name="id" value="{{ customer_variant_id }}">
        {% else %}
          <input type="hidden" class="variant" name="id" value="{{ product.variants.first.id }}">
        {% endif %}
        {%- if gift_card_recipient_feature_active -%}
          {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
        {%- endif -%}

        <div class="product-form__buttons">
          {%- liquid
            assign check_against_inventory = true
            if product.selected_or_first_available_variant.inventory_management != 'shopify' or product.selected_or_first_available_variant.inventory_policy == 'continue'
              assign check_against_inventory = false
            endif
            if product.selected_or_first_available_variant.quantity_rule.min > product.selected_or_first_available_variant.inventory_quantity and check_against_inventory
              assign quantity_rule_soldout = true
            endif
          -%}
          <button
            id="ProductSubmitButton-{{ section_id }}"
            type="submit"
            name="add"
            class="product-form__submit button button--full-width {% if show_dynamic_checkout %}button--secondary{% else %}button--primary{% endif %}"
            {% if product.selected_or_first_available_variant.available == false
              or quantity_rule_soldout
              or product.selected_or_first_available_variant == null
            %}
              disabled
            {% endif %}
          >
            <span>
              {%- if product.selected_or_first_available_variant == null -%}
                {{ 'products.product.unavailable' | t }}
              {%- elsif product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%}
                {{ 'products.product.sold_out' | t }}
              {%- else -%}
                {{ 'products.product.add_to_cart' | t }}
              {%- endif -%}
            </span>
            {%- render 'loading-spinner' -%}
          </button>
          {%- if show_dynamic_checkout -%}
              <!-- form | payment_button  -->
          {%- endif -%}
        </div>
      {%- endform -%}
    </product-form>
  {%- else -%}
    <div class="product-form">
      <div class="product-form__buttons form">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary"
          disabled
        >
          {{ 'products.product.sold_out' | t }}
        </button>
      </div>
    </div>
  {%- endif -%}

  {%- if show_pickup_availability -%}
    {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}

    {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities
      | where: 'pick_up_enabled', true
    -%}

    <pickup-availability
      class="product__pickup-availabilities quick-add-hidden"
      {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
        available
      {% endif %}
      data-root-url="{{ routes.root_url }}"
      data-variant-id="{{ product.selected_or_first_available_variant.id }}"
      data-has-only-default-variant="{{ product.has_only_default_variant }}"
      data-product-page-color-scheme="gradient color-{{ section.settings.color_scheme }}"
    >
      <template>
        <pickup-availability-preview class="pickup-availability-preview">
          <span class="svg-wrapper">
            {{- 'icon-unavailable.svg' | inline_asset_content -}}
          </span>
          <div class="pickup-availability-info">
            <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
            <button class="pickup-availability-button link link--text underlined-link">
              {{ 'products.product.pickup_availability.refresh' | t }}
            </button>
          </div>
        </pickup-availability-preview>
      </template>
    </pickup-availability>

    <script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
</div>
