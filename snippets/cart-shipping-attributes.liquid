{%- liquid
  assign is_member_customer = false
  if customer and customer.metafields.custom.dismember == true
    assign is_member_customer = true
  endif
-%}

<script>
  // Add shipping attributes to cart when customer status is determined
  (function() {
    var isMember = {{ is_member_customer }};
    var shippingCode = isMember ? 'NS' : '180';
    var shippingMethod = isMember ? 'free' : 'paid';

    console.log('Setting cart attributes - Member:', isMember, 'Code:', shippingCode);

    fetch('/cart/update.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        attributes: {
          'customer_member': isMember.toString(),
          'shipping_code': shippingCode,
          'shipping_method': shippingMethod,
          'customer_id': '{{ customer.id | default: "guest" }}'
        }
      })
    })
    .then(response => response.json())
    .then(data => {
      console.log('Cart attributes updated:', data.attributes);
    })
    .catch(error => {
      console.error('Error updating cart attributes:', error);
    });
  })();
</script>
