{% if customer %}
{%- liquid
assign is_member_customer = false
if customer and customer and customer.metafields.custom.ismember == true or customer.metafields.custom.IsMember == true
  assign is_member_customer = true
endif
-%}

<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
  <strong>Debug - Shipping Info:</strong><br>
  Customer: {{ customer.email }}<br>
  Member Status: {{ is_member_customer }}<br>
  Shipping Code: {{ is_member_customer | ternary: 'NS', '180' }}<br>
  Shipping Method: {{ is_member_customer | ternary: 'free', 'paid' }}<br>
  
  <div id="cart-attributes-display">
    Current Cart Attributes: <span id="attributes-list">Loading...</span>
  </div>
</div>

<script>
// Display current cart attributes
fetch('/cart.js')
  .then(response => response.json())
  .then(cart => {
    document.getElementById('attributes-list').textContent = JSON.stringify(cart.attributes, null, 2);
  });
</script>
{% endif %}