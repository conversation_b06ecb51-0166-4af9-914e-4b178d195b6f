{%- liquid
assign is_member_customer = false
if customer and customer and customer.metafields.custom.ismember == true or customer.metafields.custom.IsMember == true
  assign is_member_customer = true
endif
-%}

{% assign has_member = false %}
{% assign has_list = false %}

{% for option in product.options_with_values %}
  {% for value in option.values %}
    {% assign value_down = value | downcase %}
    {% if value_down == 'member' %}
      {% assign has_member = true %}
    {% endif %}
    {% if value_down == 'list' %}
      {% assign has_list = true %}
    {% endif %}
  {% endfor %}
{% endfor %}

{% if has_member and has_list %}
  {% for option in product.options_with_values %}
    {% for value in option.values %}
      {% assign matching_variant = product.variants | where: 'option1', value | first %}
        {% assign value_down = value | downcase %}
        {% if value_down == 'member' and is_member_customer %}
          <b>Your Price - </b>
        {% elsif value_down == 'list' and is_member_customer == false %}
          <b>Your Price - </b>
        {% else %}
          <b>{{ value_down | capitalize }} Price - </b>
        {% endif %}
      {% if matching_variant %}
        <span>{{ matching_variant.price | money }}</span>
      {% else %}
        Price not available
      {% endif %}
      <br>
    {% endfor %}
  {% endfor %}
{% else %}
  {% render 'price', product: product %}
{% endif %}
