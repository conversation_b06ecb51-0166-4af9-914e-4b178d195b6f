// Example webhook handler (adjust for your system)
function handleOrderWebhook(orderData) {
  console.log('Order received:', orderData.id);
  
  // Get shipping attributes from cart
  const attributes = orderData.note_attributes || [];
  
  const customerMember = attributes.find(attr => attr.name === 'customer_member')?.value === 'true';
  const shippingCode = attributes.find(attr => attr.name === 'shipping_code')?.value || '180';
  const shippingMethod = attributes.find(attr => attr.name === 'shipping_method')?.value || 'paid';
  const customerId = attributes.find(attr => attr.name === 'customer_id')?.value;
  
  console.log('Shipping Info:', {
    customerMember,
    shippingCode,
    shippingMethod,
    customerId
  });
  
  // Your shipping logic here
  if (customerMember && shippingCode === 'NS') {
    // Process free shipping for member
    processShipping(orderData, 'free', 0);
  } else if (!customerMember && shippingCode === '180') {
    // Process paid shipping for non-member
    const shippingCost = calculateShippingCost(orderData.total_price);
    processShipping(orderData, 'paid', shippingCost);
  }
}